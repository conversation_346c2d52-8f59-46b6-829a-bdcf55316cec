# Test build script using system Gradle or IDE Gradle
Write-Host "Testing project configuration..."

# Try to find <PERSON>rad<PERSON> in common locations
$gradlePaths = @(
    "C:\Program Files\JetBrains\IntelliJ IDEA*\plugins\gradle\lib\gradle-*\bin\gradle.bat",
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-*\*\gradle-*\bin\gradle.bat",
    "$env:GRADLE_HOME\bin\gradle.bat"
)

$gradleCmd = $null
foreach ($path in $gradlePaths) {
    $resolved = Get-ChildItem $path -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($resolved) {
        $gradleCmd = $resolved.FullName
        Write-Host "Found Gradle at: $gradleCmd"
        break
    }
}

if (-not $gradleCmd) {
    Write-Host "No Gradle installation found. Please install Gradle or use IntelliJ IDEA to build the project."
    Write-Host ""
    Write-Host "Alternative solutions:"
    Write-Host "1. Install Gradle manually from https://gradle.org/install/"
    Write-Host "2. Use IntelliJ IDEA's built-in Gradle support"
    Write-Host "3. Use Android Studio's built-in Gradle support"
    exit 1
}

# Test basic Gradle tasks
Write-Host "Testing Gradle tasks..."
try {
    & $gradleCmd tasks --all
    Write-Host "Gradle configuration is valid!"
} catch {
    Write-Host "Gradle configuration has issues: $_"
    exit 1
}
